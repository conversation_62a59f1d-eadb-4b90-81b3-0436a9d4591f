"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ChatArea.tsx":
/*!*************************************!*\
  !*** ./src/components/ChatArea.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatArea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _MessageBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MessageBubble */ \"(app-pages-browser)/./src/components/MessageBubble.tsx\");\n/* harmony import */ var _ProgressIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ProgressIndicator */ \"(app-pages-browser)/./src/components/ProgressIndicator.tsx\");\n/* harmony import */ var _MemoryInsights__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MemoryInsights */ \"(app-pages-browser)/./src/components/MemoryInsights.tsx\");\n/* harmony import */ var _FileNotification__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./FileNotification */ \"(app-pages-browser)/./src/components/FileNotification.tsx\");\n/* harmony import */ var _contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/SettingsContext */ \"(app-pages-browser)/./src/contexts/SettingsContext.tsx\");\n/* harmony import */ var _hooks_useFileWatcher__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useFileWatcher */ \"(app-pages-browser)/./src/hooks/useFileWatcher.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ChatArea(param) {\n    let { messages, isLoading = false, error, currentMode = 'simple', progressStatus, onEditMessage, onResendMessage, onDeleteMessage, zepSessionId, showFileNotifications = true } = param;\n    _s();\n    const { settings } = (0,_contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_7__.useSettings)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // File watcher for notifications\n    const { newFiles, clearNotifications } = (0,_hooks_useFileWatcher__WEBPACK_IMPORTED_MODULE_8__.useFileWatcher)(showFileNotifications);\n    const scrollToBottom = ()=>{\n        if (messagesEndRef.current) {\n            const chatContainer = messagesEndRef.current.closest('.overflow-y-auto');\n            if (chatContainer) {\n                chatContainer.scrollTop = chatContainer.scrollHeight;\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatArea.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatArea.useEffect\"], [\n        messages\n    ]);\n    if (messages.length === 0 && !isLoading && !error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex items-center justify-center bg-background\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center max-w-md mx-auto p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-white text-2xl font-bold\",\n                            children: \"N\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-foreground mb-2\",\n                        children: \"Welcome to Nexus\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground mb-6\",\n                        children: \"Your AI-powered chat assistant with enhanced capabilities. Choose your mode and start a conversation.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 text-sm text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Simple Chat:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 18\n                                    }, this),\n                                    \" Fast AI responses\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Enhanced Chat:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 18\n                                    }, this),\n                                    \" AI with tools and web search\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Research Mode:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 18\n                                    }, this),\n                                    \" Multi-agent deep analysis\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Your conversations are saved locally\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this);\n    }\n    // ChatArea component render\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex overflow-hidden bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto min-h-full flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                newFiles.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FileNotification__WEBPACK_IMPORTED_MODULE_6__.FileNotification, {\n                                            fileName: file.name,\n                                            fileSize: file.size,\n                                            createdAt: file.created,\n                                            onViewFiles: ()=>router.push('/files'),\n                                            onDismiss: ()=>{\n                                                // Remove this specific notification\n                                                const updatedFiles = newFiles.filter((_, i)=>i !== index);\n                                                if (updatedFiles.length === 0) {\n                                                    clearNotifications();\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, \"file-\".concat(file.name, \"-\").concat(index), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 17\n                                    }, this)),\n                                messages.map((message)=>{\n                                    // For enhanced/orchestrator modes, hide empty streaming assistant messages\n                                    // to avoid showing duplicate \"Nexus\" entries (one empty + one progress indicator)\n                                    const shouldHideEmptyStreaming = message.role === 'assistant' && message.isStreaming && message.content === '' && (currentMode === 'enhanced' || currentMode === 'orchestrator') && isLoading;\n                                    if (shouldHideEmptyStreaming) {\n                                        return null;\n                                    }\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageBubble__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        message: message,\n                                        onEditMessage: onEditMessage,\n                                        onResendMessage: onResendMessage,\n                                        onDeleteMessage: onDeleteMessage\n                                    }, \"\".concat(message.id, \"-\").concat(message.timestamp), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 19\n                                    }, this);\n                                }).filter(Boolean),\n                                isLoading && (currentMode === 'enhanced' || currentMode === 'orchestrator') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProgressIndicator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    isVisible: true,\n                                    currentStatus: progressStatus,\n                                    mode: currentMode === 'orchestrator' ? 'orchestrator' : 'enhanced'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 mx-4 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white text-xs font-bold\",\n                                                        children: \"!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 mb-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-red-800 font-medium\",\n                                                                    children: \"Error\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                                                    lineNumber: 160,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                currentMode !== 'simple' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-red-600 bg-red-100 px-2 py-0.5 rounded-full\",\n                                                                    children: currentMode === 'orchestrator' ? 'Research Mode' : 'Enhanced Mode'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-600 text-sm mb-2\",\n                                                            children: error\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        currentMode !== 'simple' && error.includes('unavailable') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-red-500 bg-red-100 p-2 rounded border-l-2 border-red-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium mb-1\",\n                                                                    children: \"\\uD83D\\uDCA1 Suggestion:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Try switching to Simple Chat mode for basic AI responses, or check if the enhanced service is running.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: messagesEndRef\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            settings.zepSettings.enabled && settings.zepSettings.showMemoryInsights && (currentMode === 'enhanced' || currentMode === 'orchestrator') && zepSessionId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-80 border-l border-gray-200 bg-gray-50 overflow-y-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MemoryInsights__WEBPACK_IMPORTED_MODULE_5__.MemoryInsights, {\n                        sessionId: zepSessionId\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                lineNumber: 192,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatArea, \"vhu/A0i6FwoD+ParmAdWE7F+TN0=\", false, function() {\n    return [\n        _contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_7__.useSettings,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useFileWatcher__WEBPACK_IMPORTED_MODULE_8__.useFileWatcher\n    ];\n});\n_c = ChatArea;\nvar _c;\n$RefreshReg$(_c, \"ChatArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0NoYXRBcmVhLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBRTBDO0FBQ0U7QUFFQTtBQUNRO0FBQ0Y7QUFDSTtBQUNHO0FBQ0Q7QUFrQnpDLFNBQVNTLFNBQVMsS0FXakI7UUFYaUIsRUFDL0JDLFFBQVEsRUFDUkMsWUFBWSxLQUFLLEVBQ2pCQyxLQUFLLEVBQ0xDLGNBQWMsUUFBUSxFQUN0QkMsY0FBYyxFQUNkQyxhQUFhLEVBQ2JDLGVBQWUsRUFDZkMsZUFBZSxFQUNmQyxZQUFZLEVBQ1pDLHdCQUF3QixJQUFJLEVBQ2QsR0FYaUI7O0lBWS9CLE1BQU0sRUFBRUMsUUFBUSxFQUFFLEdBQUdiLHNFQUFXQTtJQUNoQyxNQUFNYyxTQUFTbkIsMERBQVNBO0lBQ3hCLE1BQU1vQixpQkFBaUJyQiw2Q0FBTUEsQ0FBaUI7SUFFOUMsaUNBQWlDO0lBQ2pDLE1BQU0sRUFBRXNCLFFBQVEsRUFBRUMsa0JBQWtCLEVBQUUsR0FBR2hCLHFFQUFjQSxDQUFDVztJQUl4RCxNQUFNTSxpQkFBaUI7UUFDckIsSUFBSUgsZUFBZUksT0FBTyxFQUFFO1lBQzFCLE1BQU1DLGdCQUFnQkwsZUFBZUksT0FBTyxDQUFDRSxPQUFPLENBQUM7WUFDckQsSUFBSUQsZUFBZTtnQkFDakJBLGNBQWNFLFNBQVMsR0FBR0YsY0FBY0csWUFBWTtZQUN0RDtRQUNGO0lBQ0Y7SUFFQTlCLGdEQUFTQTs4QkFBQztZQUNSeUI7UUFDRjs2QkFBRztRQUFDZjtLQUFTO0lBRWIsSUFBSUEsU0FBU3FCLE1BQU0sS0FBSyxLQUFLLENBQUNwQixhQUFhLENBQUNDLE9BQU87UUFDakQscUJBQ0UsOERBQUNvQjtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDQzs0QkFBS0QsV0FBVTtzQ0FBZ0M7Ozs7Ozs7Ozs7O2tDQUVsRCw4REFBQ0U7d0JBQUdGLFdBQVU7a0NBQTBDOzs7Ozs7a0NBR3hELDhEQUFDRzt3QkFBRUgsV0FBVTtrQ0FBNkI7Ozs7OztrQ0FHMUMsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0c7O29DQUFFO2tEQUFFLDhEQUFDQztrREFBTzs7Ozs7O29DQUFxQjs7Ozs7OzswQ0FDbEMsOERBQUNEOztvQ0FBRTtrREFBRSw4REFBQ0M7a0RBQU87Ozs7OztvQ0FBdUI7Ozs7Ozs7MENBQ3BDLDhEQUFDRDs7b0NBQUU7a0RBQUUsOERBQUNDO2tEQUFPOzs7Ozs7b0NBQXVCOzs7Ozs7OzBDQUNwQyw4REFBQ0Q7MENBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBS2I7SUFFQSw0QkFBNEI7SUFFNUIscUJBQ0UsOERBQUNKO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7Z0NBRVpWLFNBQVNlLEdBQUcsQ0FBQyxDQUFDQyxNQUFNQyxzQkFDbkIsOERBQUNSO3dDQUF1Q0MsV0FBVTtrREFDaEQsNEVBQUMzQiwrREFBZ0JBOzRDQUNmbUMsVUFBVUYsS0FBS0csSUFBSTs0Q0FDbkJDLFVBQVVKLEtBQUtLLElBQUk7NENBQ25CQyxXQUFXTixLQUFLTyxPQUFPOzRDQUN2QkMsYUFBYSxJQUFNMUIsT0FBTzJCLElBQUksQ0FBQzs0Q0FDL0JDLFdBQVc7Z0RBQ1Qsb0NBQW9DO2dEQUNwQyxNQUFNQyxlQUFlM0IsU0FBUzRCLE1BQU0sQ0FBQyxDQUFDQyxHQUFHQyxJQUFNQSxNQUFNYjtnREFDckQsSUFBSVUsYUFBYW5CLE1BQU0sS0FBSyxHQUFHO29EQUM3QlA7Z0RBQ0Y7NENBQ0Y7Ozs7Ozt1Q0FaTSxRQUFxQmdCLE9BQWJELEtBQUtHLElBQUksRUFBQyxLQUFTLE9BQU5GOzs7OztnQ0FpQmhDOUIsU0FBUzRCLEdBQUcsQ0FBQyxDQUFDZ0I7b0NBQ2IsMkVBQTJFO29DQUMzRSxrRkFBa0Y7b0NBQ2xGLE1BQU1DLDJCQUNKRCxRQUFRRSxJQUFJLEtBQUssZUFDakJGLFFBQVFHLFdBQVcsSUFDbkJILFFBQVFJLE9BQU8sS0FBSyxNQUNuQjdDLENBQUFBLGdCQUFnQixjQUFjQSxnQkFBZ0IsY0FBYSxLQUM1REY7b0NBRUYsSUFBSTRDLDBCQUEwQjt3Q0FDNUIsT0FBTztvQ0FDVDtvQ0FFQSxxQkFDRSw4REFBQ3BELHNEQUFhQTt3Q0FFWm1ELFNBQVNBO3dDQUNUdkMsZUFBZUE7d0NBQ2ZDLGlCQUFpQkE7d0NBQ2pCQyxpQkFBaUJBO3VDQUpaLEdBQWlCcUMsT0FBZEEsUUFBUUssRUFBRSxFQUFDLEtBQXFCLE9BQWxCTCxRQUFRTSxTQUFTOzs7OztnQ0FPN0MsR0FBR1QsTUFBTSxDQUFDVTtnQ0FHWGxELGFBQWNFLENBQUFBLGdCQUFnQixjQUFjQSxnQkFBZ0IsY0FBYSxtQkFDeEUsOERBQUNULDBEQUFpQkE7b0NBQ2hCMEQsV0FBVztvQ0FDWEMsZUFBZWpEO29DQUNma0QsTUFBTW5ELGdCQUFnQixpQkFBaUIsaUJBQWlCOzs7Ozs7Z0NBTzNERCx1QkFDQyw4REFBQ29CO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ0M7d0RBQUtELFdBQVU7a0VBQStCOzs7Ozs7Ozs7Ozs4REFFakQsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRztvRUFBRUgsV0FBVTs4RUFBMkI7Ozs7OztnRUFDdkNwQixnQkFBZ0IsMEJBQ2YsOERBQUNxQjtvRUFBS0QsV0FBVTs4RUFDYnBCLGdCQUFnQixpQkFBaUIsa0JBQWtCOzs7Ozs7Ozs7Ozs7c0VBSTFELDhEQUFDdUI7NERBQUVILFdBQVU7c0VBQTZCckI7Ozs7Ozt3REFHekNDLGdCQUFnQixZQUFZRCxNQUFNcUQsUUFBUSxDQUFDLGdDQUMxQyw4REFBQ2pDOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0c7b0VBQUVILFdBQVU7OEVBQW1COzs7Ozs7OEVBQ2hDLDhEQUFDRzs4RUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FRZiw4REFBQ0o7b0NBQUlrQyxLQUFLNUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU9qQkYsU0FBUytDLFdBQVcsQ0FBQ0MsT0FBTyxJQUM1QmhELFNBQVMrQyxXQUFXLENBQUNFLGtCQUFrQixJQUN0Q3hELENBQUFBLGdCQUFnQixjQUFjQSxnQkFBZ0IsY0FBYSxLQUM1REssOEJBQ0MsOERBQUNjO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQzVCLDJEQUFjQTt3QkFBQ2lFLFdBQVdwRDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU12QztHQTNLd0JUOztRQVlERixrRUFBV0E7UUFDakJMLHNEQUFTQTtRQUlpQk0saUVBQWNBOzs7S0FqQmpDQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSXFxEZXZcXEFJXFxDaGF0XFxjaGF0MTFcXGFpLWNoYXQtYXBwXFxzcmNcXGNvbXBvbmVudHNcXENoYXRBcmVhLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IE1lc3NhZ2UgfSBmcm9tICdAL3R5cGVzL2NoYXQnO1xuaW1wb3J0IE1lc3NhZ2VCdWJibGUgZnJvbSAnLi9NZXNzYWdlQnViYmxlJztcbmltcG9ydCBQcm9ncmVzc0luZGljYXRvciBmcm9tICcuL1Byb2dyZXNzSW5kaWNhdG9yJztcbmltcG9ydCB7IE1lbW9yeUluc2lnaHRzIH0gZnJvbSAnLi9NZW1vcnlJbnNpZ2h0cyc7XG5pbXBvcnQgeyBGaWxlTm90aWZpY2F0aW9uIH0gZnJvbSAnLi9GaWxlTm90aWZpY2F0aW9uJztcbmltcG9ydCB7IHVzZVNldHRpbmdzIH0gZnJvbSAnQC9jb250ZXh0cy9TZXR0aW5nc0NvbnRleHQnO1xuaW1wb3J0IHsgdXNlRmlsZVdhdGNoZXIgfSBmcm9tICdAL2hvb2tzL3VzZUZpbGVXYXRjaGVyJztcbmltcG9ydCB7IGNuIH0gZnJvbSAnQC9saWIvdXRpbHMnO1xuXG5leHBvcnQgdHlwZSBDaGF0TW9kZSA9ICdzaW1wbGUnIHwgJ2VuaGFuY2VkJyB8ICdvcmNoZXN0cmF0b3InO1xuXG5pbnRlcmZhY2UgQ2hhdEFyZWFQcm9wcyB7XG4gIG1lc3NhZ2VzOiBNZXNzYWdlW107XG4gIGlzTG9hZGluZz86IGJvb2xlYW47XG4gIGVycm9yPzogc3RyaW5nIHwgbnVsbDtcbiAgY3VycmVudE1vZGU/OiBDaGF0TW9kZTtcbiAgcHJvZ3Jlc3NTdGF0dXM/OiBzdHJpbmc7XG4gIG9uRWRpdE1lc3NhZ2U/OiAobWVzc2FnZUlkOiBzdHJpbmcsIG5ld0NvbnRlbnQ6IHN0cmluZykgPT4gdm9pZDtcbiAgb25SZXNlbmRNZXNzYWdlPzogKG1lc3NhZ2VJZDogc3RyaW5nLCB1cGRhdGVkQ29udGVudD86IHN0cmluZykgPT4gdm9pZDtcbiAgb25EZWxldGVNZXNzYWdlPzogKG1lc3NhZ2VJZDogc3RyaW5nKSA9PiB2b2lkO1xuICB6ZXBTZXNzaW9uSWQ/OiBzdHJpbmcgfCBudWxsO1xuICBzaG93RmlsZU5vdGlmaWNhdGlvbnM/OiBib29sZWFuO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDaGF0QXJlYSh7XG4gIG1lc3NhZ2VzLFxuICBpc0xvYWRpbmcgPSBmYWxzZSxcbiAgZXJyb3IsXG4gIGN1cnJlbnRNb2RlID0gJ3NpbXBsZScsXG4gIHByb2dyZXNzU3RhdHVzLFxuICBvbkVkaXRNZXNzYWdlLFxuICBvblJlc2VuZE1lc3NhZ2UsXG4gIG9uRGVsZXRlTWVzc2FnZSxcbiAgemVwU2Vzc2lvbklkLFxuICBzaG93RmlsZU5vdGlmaWNhdGlvbnMgPSB0cnVlXG59OiBDaGF0QXJlYVByb3BzKSB7XG4gIGNvbnN0IHsgc2V0dGluZ3MgfSA9IHVzZVNldHRpbmdzKCk7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICBjb25zdCBtZXNzYWdlc0VuZFJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbCk7XG4gIFxuICAvLyBGaWxlIHdhdGNoZXIgZm9yIG5vdGlmaWNhdGlvbnNcbiAgY29uc3QgeyBuZXdGaWxlcywgY2xlYXJOb3RpZmljYXRpb25zIH0gPSB1c2VGaWxlV2F0Y2hlcihzaG93RmlsZU5vdGlmaWNhdGlvbnMpO1xuXG5cblxuICBjb25zdCBzY3JvbGxUb0JvdHRvbSA9ICgpID0+IHtcbiAgICBpZiAobWVzc2FnZXNFbmRSZWYuY3VycmVudCkge1xuICAgICAgY29uc3QgY2hhdENvbnRhaW5lciA9IG1lc3NhZ2VzRW5kUmVmLmN1cnJlbnQuY2xvc2VzdCgnLm92ZXJmbG93LXktYXV0bycpO1xuICAgICAgaWYgKGNoYXRDb250YWluZXIpIHtcbiAgICAgICAgY2hhdENvbnRhaW5lci5zY3JvbGxUb3AgPSBjaGF0Q29udGFpbmVyLnNjcm9sbEhlaWdodDtcbiAgICAgIH1cbiAgICB9XG4gIH07XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzY3JvbGxUb0JvdHRvbSgpO1xuICB9LCBbbWVzc2FnZXNdKTtcblxuICBpZiAobWVzc2FnZXMubGVuZ3RoID09PSAwICYmICFpc0xvYWRpbmcgJiYgIWVycm9yKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJnLWJhY2tncm91bmRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYXgtdy1tZCBteC1hdXRvIHAtOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS01MDAgdG8tcHVycGxlLTYwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi00XCI+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQtMnhsIGZvbnQtYm9sZFwiPk48L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWZvcmVncm91bmQgbWItMlwiPlxuICAgICAgICAgICAgV2VsY29tZSB0byBOZXh1c1xuICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG1iLTZcIj5cbiAgICAgICAgICAgIFlvdXIgQUktcG93ZXJlZCBjaGF0IGFzc2lzdGFudCB3aXRoIGVuaGFuY2VkIGNhcGFiaWxpdGllcy4gQ2hvb3NlIHlvdXIgbW9kZSBhbmQgc3RhcnQgYSBjb252ZXJzYXRpb24uXG4gICAgICAgICAgPC9wPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yIHRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICA8cD7igKIgPHN0cm9uZz5TaW1wbGUgQ2hhdDo8L3N0cm9uZz4gRmFzdCBBSSByZXNwb25zZXM8L3A+XG4gICAgICAgICAgICA8cD7igKIgPHN0cm9uZz5FbmhhbmNlZCBDaGF0Ojwvc3Ryb25nPiBBSSB3aXRoIHRvb2xzIGFuZCB3ZWIgc2VhcmNoPC9wPlxuICAgICAgICAgICAgPHA+4oCiIDxzdHJvbmc+UmVzZWFyY2ggTW9kZTo8L3N0cm9uZz4gTXVsdGktYWdlbnQgZGVlcCBhbmFseXNpczwvcD5cbiAgICAgICAgICAgIDxwPuKAoiBZb3VyIGNvbnZlcnNhdGlvbnMgYXJlIHNhdmVkIGxvY2FsbHk8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIC8vIENoYXRBcmVhIGNvbXBvbmVudCByZW5kZXJcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1mdWxsIGZsZXggb3ZlcmZsb3ctaGlkZGVuIGJnLWJhY2tncm91bmRcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggZmxleC1jb2wgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNHhsIG14LWF1dG8gbWluLWgtZnVsbCBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICB7LyogRmlsZSBub3RpZmljYXRpb25zICovfVxuICAgICAgICAgICAgICB7bmV3RmlsZXMubWFwKChmaWxlLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgIDxkaXYga2V5PXtgZmlsZS0ke2ZpbGUubmFtZX0tJHtpbmRleH1gfSBjbGFzc05hbWU9XCJwLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxGaWxlTm90aWZpY2F0aW9uXG4gICAgICAgICAgICAgICAgICAgIGZpbGVOYW1lPXtmaWxlLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgIGZpbGVTaXplPXtmaWxlLnNpemV9XG4gICAgICAgICAgICAgICAgICAgIGNyZWF0ZWRBdD17ZmlsZS5jcmVhdGVkfVxuICAgICAgICAgICAgICAgICAgICBvblZpZXdGaWxlcz17KCkgPT4gcm91dGVyLnB1c2goJy9maWxlcycpfVxuICAgICAgICAgICAgICAgICAgICBvbkRpc21pc3M9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAvLyBSZW1vdmUgdGhpcyBzcGVjaWZpYyBub3RpZmljYXRpb25cbiAgICAgICAgICAgICAgICAgICAgICBjb25zdCB1cGRhdGVkRmlsZXMgPSBuZXdGaWxlcy5maWx0ZXIoKF8sIGkpID0+IGkgIT09IGluZGV4KTtcbiAgICAgICAgICAgICAgICAgICAgICBpZiAodXBkYXRlZEZpbGVzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgY2xlYXJOb3RpZmljYXRpb25zKCk7XG4gICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICB7bWVzc2FnZXMubWFwKChtZXNzYWdlKSA9PiB7XG4gICAgICAgICAgICAgICAgLy8gRm9yIGVuaGFuY2VkL29yY2hlc3RyYXRvciBtb2RlcywgaGlkZSBlbXB0eSBzdHJlYW1pbmcgYXNzaXN0YW50IG1lc3NhZ2VzXG4gICAgICAgICAgICAgICAgLy8gdG8gYXZvaWQgc2hvd2luZyBkdXBsaWNhdGUgXCJOZXh1c1wiIGVudHJpZXMgKG9uZSBlbXB0eSArIG9uZSBwcm9ncmVzcyBpbmRpY2F0b3IpXG4gICAgICAgICAgICAgICAgY29uc3Qgc2hvdWxkSGlkZUVtcHR5U3RyZWFtaW5nID1cbiAgICAgICAgICAgICAgICAgIG1lc3NhZ2Uucm9sZSA9PT0gJ2Fzc2lzdGFudCcgJiZcbiAgICAgICAgICAgICAgICAgIG1lc3NhZ2UuaXNTdHJlYW1pbmcgJiZcbiAgICAgICAgICAgICAgICAgIG1lc3NhZ2UuY29udGVudCA9PT0gJycgJiZcbiAgICAgICAgICAgICAgICAgIChjdXJyZW50TW9kZSA9PT0gJ2VuaGFuY2VkJyB8fCBjdXJyZW50TW9kZSA9PT0gJ29yY2hlc3RyYXRvcicpICYmXG4gICAgICAgICAgICAgICAgICBpc0xvYWRpbmc7XG5cbiAgICAgICAgICAgICAgICBpZiAoc2hvdWxkSGlkZUVtcHR5U3RyZWFtaW5nKSB7XG4gICAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgICAgPE1lc3NhZ2VCdWJibGVcbiAgICAgICAgICAgICAgICAgICAga2V5PXtgJHttZXNzYWdlLmlkfS0ke21lc3NhZ2UudGltZXN0YW1wfWB9XG4gICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U9e21lc3NhZ2V9XG4gICAgICAgICAgICAgICAgICAgIG9uRWRpdE1lc3NhZ2U9e29uRWRpdE1lc3NhZ2V9XG4gICAgICAgICAgICAgICAgICAgIG9uUmVzZW5kTWVzc2FnZT17b25SZXNlbmRNZXNzYWdlfVxuICAgICAgICAgICAgICAgICAgICBvbkRlbGV0ZU1lc3NhZ2U9e29uRGVsZXRlTWVzc2FnZX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgfSkuZmlsdGVyKEJvb2xlYW4pfVxuXG4gICAgICAgICAgICB7LyogRW5oYW5jZWQgcHJvZ3Jlc3MgaW5kaWNhdG9yIGZvciBlbmhhbmNlZCBhbmQgb3JjaGVzdHJhdG9yIG1vZGVzICovfVxuICAgICAgICAgICAge2lzTG9hZGluZyAmJiAoY3VycmVudE1vZGUgPT09ICdlbmhhbmNlZCcgfHwgY3VycmVudE1vZGUgPT09ICdvcmNoZXN0cmF0b3InKSAmJiAoXG4gICAgICAgICAgICAgIDxQcm9ncmVzc0luZGljYXRvclxuICAgICAgICAgICAgICAgIGlzVmlzaWJsZT17dHJ1ZX1cbiAgICAgICAgICAgICAgICBjdXJyZW50U3RhdHVzPXtwcm9ncmVzc1N0YXR1c31cbiAgICAgICAgICAgICAgICBtb2RlPXtjdXJyZW50TW9kZSA9PT0gJ29yY2hlc3RyYXRvcicgPyAnb3JjaGVzdHJhdG9yJyA6ICdlbmhhbmNlZCd9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICApfVxuXG5cblxuICAgICAgICAgICAgey8qIEVuaGFuY2VkIGVycm9yIGhhbmRsaW5nICovfVxuICAgICAgICAgICAge2Vycm9yICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgbXgtNCBtYi00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgZ2FwLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wIHctNiBoLTYgYmctcmVkLTUwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQteHMgZm9udC1ib2xkXCI+ITwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC04MDAgZm9udC1tZWRpdW1cIj5FcnJvcjwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtjdXJyZW50TW9kZSAhPT0gJ3NpbXBsZScgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtcmVkLTYwMCBiZy1yZWQtMTAwIHB4LTIgcHktMC41IHJvdW5kZWQtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjdXJyZW50TW9kZSA9PT0gJ29yY2hlc3RyYXRvcicgPyAnUmVzZWFyY2ggTW9kZScgOiAnRW5oYW5jZWQgTW9kZSd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwIHRleHQtc20gbWItMlwiPntlcnJvcn08L3A+XG5cbiAgICAgICAgICAgICAgICAgICAgICB7LyogU2hvdyBmYWxsYmFjayBpbmZvIGZvciBlbmhhbmNlZCBtb2RlcyAqL31cbiAgICAgICAgICAgICAgICAgICAgICB7Y3VycmVudE1vZGUgIT09ICdzaW1wbGUnICYmIGVycm9yLmluY2x1ZGVzKCd1bmF2YWlsYWJsZScpICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXJlZC01MDAgYmctcmVkLTEwMCBwLTIgcm91bmRlZCBib3JkZXItbC0yIGJvcmRlci1yZWQtMzAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIG1iLTFcIj7wn5KhIFN1Z2dlc3Rpb246PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cD5Ucnkgc3dpdGNoaW5nIHRvIFNpbXBsZSBDaGF0IG1vZGUgZm9yIGJhc2ljIEFJIHJlc3BvbnNlcywgb3IgY2hlY2sgaWYgdGhlIGVuaGFuY2VkIHNlcnZpY2UgaXMgcnVubmluZy48L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPGRpdiByZWY9e21lc3NhZ2VzRW5kUmVmfSAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgICBcbiAgICAgIHsvKiBaZXAgTWVtb3J5IEluc2lnaHRzIFBhbmVsIC0gU2hvdyB3aGVuIGVuYWJsZWQgaW4gc2V0dGluZ3MgYW5kIGVuaGFuY2VkL29yY2hlc3RyYXRvciBtb2RlIGlzIGFjdGl2ZSAqL31cbiAgICAgIHtzZXR0aW5ncy56ZXBTZXR0aW5ncy5lbmFibGVkICYmIFxuICAgICAgIHNldHRpbmdzLnplcFNldHRpbmdzLnNob3dNZW1vcnlJbnNpZ2h0cyAmJiBcbiAgICAgICAoY3VycmVudE1vZGUgPT09ICdlbmhhbmNlZCcgfHwgY3VycmVudE1vZGUgPT09ICdvcmNoZXN0cmF0b3InKSAmJiBcbiAgICAgICB6ZXBTZXNzaW9uSWQgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctODAgYm9yZGVyLWwgYm9yZGVyLWdyYXktMjAwIGJnLWdyYXktNTAgb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTRcIj5cbiAgICAgICAgICAgIDxNZW1vcnlJbnNpZ2h0cyBzZXNzaW9uSWQ9e3plcFNlc3Npb25JZH0gLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZVJvdXRlciIsIk1lc3NhZ2VCdWJibGUiLCJQcm9ncmVzc0luZGljYXRvciIsIk1lbW9yeUluc2lnaHRzIiwiRmlsZU5vdGlmaWNhdGlvbiIsInVzZVNldHRpbmdzIiwidXNlRmlsZVdhdGNoZXIiLCJDaGF0QXJlYSIsIm1lc3NhZ2VzIiwiaXNMb2FkaW5nIiwiZXJyb3IiLCJjdXJyZW50TW9kZSIsInByb2dyZXNzU3RhdHVzIiwib25FZGl0TWVzc2FnZSIsIm9uUmVzZW5kTWVzc2FnZSIsIm9uRGVsZXRlTWVzc2FnZSIsInplcFNlc3Npb25JZCIsInNob3dGaWxlTm90aWZpY2F0aW9ucyIsInNldHRpbmdzIiwicm91dGVyIiwibWVzc2FnZXNFbmRSZWYiLCJuZXdGaWxlcyIsImNsZWFyTm90aWZpY2F0aW9ucyIsInNjcm9sbFRvQm90dG9tIiwiY3VycmVudCIsImNoYXRDb250YWluZXIiLCJjbG9zZXN0Iiwic2Nyb2xsVG9wIiwic2Nyb2xsSGVpZ2h0IiwibGVuZ3RoIiwiZGl2IiwiY2xhc3NOYW1lIiwic3BhbiIsImgyIiwicCIsInN0cm9uZyIsIm1hcCIsImZpbGUiLCJpbmRleCIsImZpbGVOYW1lIiwibmFtZSIsImZpbGVTaXplIiwic2l6ZSIsImNyZWF0ZWRBdCIsImNyZWF0ZWQiLCJvblZpZXdGaWxlcyIsInB1c2giLCJvbkRpc21pc3MiLCJ1cGRhdGVkRmlsZXMiLCJmaWx0ZXIiLCJfIiwiaSIsIm1lc3NhZ2UiLCJzaG91bGRIaWRlRW1wdHlTdHJlYW1pbmciLCJyb2xlIiwiaXNTdHJlYW1pbmciLCJjb250ZW50IiwiaWQiLCJ0aW1lc3RhbXAiLCJCb29sZWFuIiwiaXNWaXNpYmxlIiwiY3VycmVudFN0YXR1cyIsIm1vZGUiLCJpbmNsdWRlcyIsInJlZiIsInplcFNldHRpbmdzIiwiZW5hYmxlZCIsInNob3dNZW1vcnlJbnNpZ2h0cyIsInNlc3Npb25JZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatArea.tsx\n"));

/***/ })

});