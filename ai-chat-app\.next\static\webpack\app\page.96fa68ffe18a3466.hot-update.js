"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ProgressIndicator.tsx":
/*!**********************************************!*\
  !*** ./src/components/ProgressIndicator.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProgressIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CpuChipIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CpuChipIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ProgressIndicator(param) {\n    let { isVisible, currentStatus, mode = 'enhanced' } = param;\n    _s();\n    const [dots, setDots] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProgressIndicator.useEffect\": ()=>{\n            if (!isVisible) return;\n            const interval = setInterval({\n                \"ProgressIndicator.useEffect.interval\": ()=>{\n                    setDots({\n                        \"ProgressIndicator.useEffect.interval\": (prev)=>{\n                            if (prev.length >= 3) return '';\n                            return prev + '.';\n                        }\n                    }[\"ProgressIndicator.useEffect.interval\"]);\n                }\n            }[\"ProgressIndicator.useEffect.interval\"], 500);\n            return ({\n                \"ProgressIndicator.useEffect\": ()=>clearInterval(interval)\n            })[\"ProgressIndicator.useEffect\"];\n        }\n    }[\"ProgressIndicator.useEffect\"], [\n        isVisible\n    ]);\n    if (!isVisible) return null;\n    const getStatusIcon = (status)=>{\n        if (status.includes('🚀') || status.includes('Starting')) return '🚀';\n        if (status.includes('🧠') || status.includes('Breaking')) return '🧠';\n        if (status.includes('⚡') || status.includes('Launching')) return '⚡';\n        if (status.includes('🔄') || status.includes('Synthesizing')) return '🔄';\n        if (status.includes('🔍') || status.includes('search')) return '🔍';\n        if (status.includes('🧮') || status.includes('calculat')) return '🧮';\n        if (status.includes('📁') || status.includes('file')) return '📁';\n        return '⚙️';\n    };\n    const getModeConfig = ()=>{\n        if (mode === 'orchestrator') {\n            return {\n                title: 'Research Mode Active',\n                subtitle: 'Multi-agent analysis in progress',\n                color: 'purple',\n                bgColor: 'bg-purple-50',\n                borderColor: 'border-purple-200',\n                textColor: 'text-purple-800',\n                iconColor: 'text-purple-600'\n            };\n        }\n        return {\n            title: 'Enhanced Mode Active',\n            subtitle: 'AI tools and capabilities enabled',\n            color: 'blue',\n            bgColor: 'bg-blue-50',\n            borderColor: 'border-blue-200',\n            textColor: 'text-blue-800',\n            iconColor: 'text-blue-600'\n        };\n    };\n    const config = getModeConfig();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"group flex gap-4 p-4 relative bg-muted/30\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center bg-green-600 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CpuChipIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium text-muted-foreground mb-1\",\n                        children: \"Nexus\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\\n              w-4 h-4 border-2 border-\".concat(config.color, \"-200 border-t-\").concat(config.color, \"-600\\n              rounded-full animate-spin\\n            \")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium \".concat(config.textColor),\n                                                        children: config.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: dots\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-600\",\n                                                children: config.subtitle\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this),\n                            currentStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: getStatusIcon(currentStatus)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: currentStatus\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this),\n                            mode === 'orchestrator' && !currentStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1 mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-xs text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-purple-400 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Analyzing query complexity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-xs text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-purple-300 rounded-full animate-pulse delay-100\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Deploying specialized agents\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-xs text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-purple-200 rounded-full animate-pulse delay-200\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Gathering comprehensive insights\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this),\n                            mode === 'enhanced' && !currentStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1 mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-xs text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Processing with AI tools\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-xs text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-blue-300 rounded-full animate-pulse delay-100\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Accessing real-time information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            mode === 'orchestrator' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full bg-purple-200 rounded-full h-1.5\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-purple-600 h-1.5 rounded-full animate-pulse\",\n                        style: {\n                            width: '60%'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n_s(ProgressIndicator, \"x5A7Sg53ZjAlz59b/ycs4VK0Y+g=\");\n_c = ProgressIndicator;\nvar _c;\n$RefreshReg$(_c, \"ProgressIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProgressIndicator.tsx\n"));

/***/ })

});