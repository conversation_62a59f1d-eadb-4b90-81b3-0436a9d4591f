"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ProgressIndicator.tsx":
/*!**********************************************!*\
  !*** ./src/components/ProgressIndicator.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProgressIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CpuChipIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CpuChipIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ProgressIndicator(param) {\n    let { isVisible, currentStatus, mode = 'enhanced' } = param;\n    _s();\n    const [dots, setDots] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProgressIndicator.useEffect\": ()=>{\n            if (!isVisible) return;\n            const interval = setInterval({\n                \"ProgressIndicator.useEffect.interval\": ()=>{\n                    setDots({\n                        \"ProgressIndicator.useEffect.interval\": (prev)=>{\n                            if (prev.length >= 3) return '';\n                            return prev + '.';\n                        }\n                    }[\"ProgressIndicator.useEffect.interval\"]);\n                }\n            }[\"ProgressIndicator.useEffect.interval\"], 500);\n            return ({\n                \"ProgressIndicator.useEffect\": ()=>clearInterval(interval)\n            })[\"ProgressIndicator.useEffect\"];\n        }\n    }[\"ProgressIndicator.useEffect\"], [\n        isVisible\n    ]);\n    if (!isVisible) return null;\n    const getStatusIcon = (status)=>{\n        if (status.includes('🚀') || status.includes('Starting')) return '🚀';\n        if (status.includes('🧠') || status.includes('Breaking')) return '🧠';\n        if (status.includes('⚡') || status.includes('Launching')) return '⚡';\n        if (status.includes('🔄') || status.includes('Synthesizing')) return '🔄';\n        if (status.includes('🔍') || status.includes('search')) return '🔍';\n        if (status.includes('🧮') || status.includes('calculat')) return '🧮';\n        if (status.includes('📁') || status.includes('file')) return '📁';\n        return '⚙️';\n    };\n    const getModeConfig = ()=>{\n        if (mode === 'orchestrator') {\n            return {\n                title: 'Research Mode Active',\n                subtitle: 'Multi-agent analysis in progress',\n                color: 'purple',\n                bgColor: 'bg-purple-50',\n                borderColor: 'border-purple-200',\n                textColor: 'text-purple-800',\n                iconColor: 'text-purple-600'\n            };\n        }\n        return {\n            title: 'Enhanced Mode Active',\n            subtitle: 'AI tools and capabilities enabled',\n            color: 'blue',\n            bgColor: 'bg-blue-50',\n            borderColor: 'border-blue-200',\n            textColor: 'text-blue-800',\n            iconColor: 'text-blue-600'\n        };\n    };\n    const config = getModeConfig();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"group flex gap-4 p-4 relative bg-muted/30\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center bg-green-600 text-white mt-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CpuChipIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium text-muted-foreground mb-1\",\n                        children: \"Nexus\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\\n              w-4 h-4 border-2 border-\".concat(config.color, \"-200 border-t-\").concat(config.color, \"-600\\n              rounded-full animate-spin\\n            \")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-foreground\",\n                                        children: config.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: dots\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground mb-2\",\n                                children: config.subtitle\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            currentStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: getStatusIcon(currentStatus)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-foreground\",\n                                        children: currentStatus\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 13\n                            }, this),\n                            mode === 'orchestrator' && !currentStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1 mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-xs text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-purple-400 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Analyzing query complexity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-xs text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-purple-300 rounded-full animate-pulse delay-100\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Deploying specialized agents\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-xs text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-purple-200 rounded-full animate-pulse delay-200\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Gathering comprehensive insights\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, this),\n                            mode === 'enhanced' && !currentStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1 mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-xs text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Processing with AI tools\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-xs text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-blue-300 rounded-full animate-pulse delay-100\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Accessing real-time information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this),\n                            mode === 'orchestrator' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full bg-purple-200 rounded-full h-1.5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-600 h-1.5 rounded-full animate-pulse\",\n                                        style: {\n                                            width: '60%'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n_s(ProgressIndicator, \"x5A7Sg53ZjAlz59b/ycs4VK0Y+g=\");\n_c = ProgressIndicator;\nvar _c;\n$RefreshReg$(_c, \"ProgressIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProgressIndicator.tsx\n"));

/***/ })

});