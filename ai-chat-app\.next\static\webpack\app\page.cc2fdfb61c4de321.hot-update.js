"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ChatArea.tsx":
/*!*************************************!*\
  !*** ./src/components/ChatArea.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatArea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _MessageBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MessageBubble */ \"(app-pages-browser)/./src/components/MessageBubble.tsx\");\n/* harmony import */ var _ProgressIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ProgressIndicator */ \"(app-pages-browser)/./src/components/ProgressIndicator.tsx\");\n/* harmony import */ var _MemoryInsights__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MemoryInsights */ \"(app-pages-browser)/./src/components/MemoryInsights.tsx\");\n/* harmony import */ var _FileNotification__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./FileNotification */ \"(app-pages-browser)/./src/components/FileNotification.tsx\");\n/* harmony import */ var _contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/SettingsContext */ \"(app-pages-browser)/./src/contexts/SettingsContext.tsx\");\n/* harmony import */ var _hooks_useFileWatcher__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useFileWatcher */ \"(app-pages-browser)/./src/hooks/useFileWatcher.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ChatArea(param) {\n    let { messages, isLoading = false, error, currentMode = 'simple', progressStatus, onEditMessage, onResendMessage, onDeleteMessage, zepSessionId, showFileNotifications = true } = param;\n    _s();\n    const { settings } = (0,_contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_7__.useSettings)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // File watcher for notifications\n    const { newFiles, clearNotifications } = (0,_hooks_useFileWatcher__WEBPACK_IMPORTED_MODULE_8__.useFileWatcher)(showFileNotifications);\n    const scrollToBottom = ()=>{\n        if (messagesEndRef.current) {\n            const chatContainer = messagesEndRef.current.closest('.overflow-y-auto');\n            if (chatContainer) {\n                chatContainer.scrollTop = chatContainer.scrollHeight;\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatArea.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatArea.useEffect\"], [\n        messages\n    ]);\n    if (messages.length === 0 && !isLoading && !error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex items-center justify-center bg-background\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center max-w-md mx-auto p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-white text-2xl font-bold\",\n                            children: \"N\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-foreground mb-2\",\n                        children: \"Welcome to Nexus\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground mb-6\",\n                        children: \"Your AI-powered chat assistant with enhanced capabilities. Choose your mode and start a conversation.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 text-sm text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Simple Chat:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 18\n                                    }, this),\n                                    \" Fast AI responses\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Enhanced Chat:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 18\n                                    }, this),\n                                    \" AI with tools and web search\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Research Mode:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 18\n                                    }, this),\n                                    \" Multi-agent deep analysis\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Your conversations are saved locally\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this);\n    }\n    // ChatArea component render\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex overflow-hidden bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto min-h-full flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                newFiles.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FileNotification__WEBPACK_IMPORTED_MODULE_6__.FileNotification, {\n                                            fileName: file.name,\n                                            fileSize: file.size,\n                                            createdAt: file.created,\n                                            onViewFiles: ()=>router.push('/files'),\n                                            onDismiss: ()=>{\n                                                // Remove this specific notification\n                                                const updatedFiles = newFiles.filter((_, i)=>i !== index);\n                                                if (updatedFiles.length === 0) {\n                                                    clearNotifications();\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, \"file-\".concat(file.name, \"-\").concat(index), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 17\n                                    }, this)),\n                                messages.map((message)=>{\n                                    // For enhanced/orchestrator modes, hide empty streaming assistant messages\n                                    // to avoid showing duplicate \"Nexus\" entries (one empty + one progress indicator)\n                                    const shouldHideEmptyStreaming = message.role === 'assistant' && message.isStreaming && message.content === '' && (currentMode === 'enhanced' || currentMode === 'orchestrator') && isLoading;\n                                    if (shouldHideEmptyStreaming) {\n                                        return null;\n                                    }\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageBubble__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        message: message,\n                                        onEditMessage: onEditMessage,\n                                        onResendMessage: onResendMessage,\n                                        onDeleteMessage: onDeleteMessage\n                                    }, \"\".concat(message.id, \"-\").concat(message.timestamp), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 19\n                                    }, this);\n                                }).filter(Boolean),\n                                isLoading && (currentMode === 'enhanced' || currentMode === 'orchestrator') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProgressIndicator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        isVisible: true,\n                                        currentStatus: progressStatus,\n                                        mode: currentMode === 'orchestrator' ? 'orchestrator' : 'enhanced'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 mx-4 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white text-xs font-bold\",\n                                                        children: \"!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 mb-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-red-800 font-medium\",\n                                                                    children: \"Error\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                currentMode !== 'simple' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-red-600 bg-red-100 px-2 py-0.5 rounded-full\",\n                                                                    children: currentMode === 'orchestrator' ? 'Research Mode' : 'Enhanced Mode'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                                                    lineNumber: 164,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-600 text-sm mb-2\",\n                                                            children: error\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        currentMode !== 'simple' && error.includes('unavailable') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-red-500 bg-red-100 p-2 rounded border-l-2 border-red-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium mb-1\",\n                                                                    children: \"\\uD83D\\uDCA1 Suggestion:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                                                    lineNumber: 174,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Try switching to Simple Chat mode for basic AI responses, or check if the enhanced service is running.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                                                    lineNumber: 175,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: messagesEndRef\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            settings.zepSettings.enabled && settings.zepSettings.showMemoryInsights && (currentMode === 'enhanced' || currentMode === 'orchestrator') && zepSessionId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-80 border-l border-gray-200 bg-gray-50 overflow-y-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MemoryInsights__WEBPACK_IMPORTED_MODULE_5__.MemoryInsights, {\n                        sessionId: zepSessionId\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n                lineNumber: 194,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ChatArea.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatArea, \"vhu/A0i6FwoD+ParmAdWE7F+TN0=\", false, function() {\n    return [\n        _contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_7__.useSettings,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useFileWatcher__WEBPACK_IMPORTED_MODULE_8__.useFileWatcher\n    ];\n});\n_c = ChatArea;\nvar _c;\n$RefreshReg$(_c, \"ChatArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatArea.tsx\n"));

/***/ })

});