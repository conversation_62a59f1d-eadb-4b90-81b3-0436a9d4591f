'use client';

import { useState, useEffect } from 'react';
import { Message } from '@/types/chat';
import { UserIcon, CpuChipIcon, PencilIcon, ClipboardIcon, CheckIcon, TrashIcon } from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import MermaidChart from './MermaidChart';

interface MessageBubbleProps {
  message: Message;
  onEditMessage?: (messageId: string, newContent: string) => void;
  onResendMessage?: (messageId: string, updatedContent?: string) => void;
  onDeleteMessage?: (messageId: string) => void;
}

export default function MessageBubble({ message, onEditMessage, onResendMessage, onDeleteMessage }: MessageBubbleProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(message.content);
  const [copied, setCopied] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // Debug: Log when component receives new message prop (only on message ID change)
  useEffect(() => {
    console.log('MessageBubble received new message:', {
      messageId: message.id,
      role: message.role,
      contentLength: message.content.length
    });
  }, [message.id]);

  // Update editContent when message content changes (but not during editing)
  useEffect(() => {
    if (!isEditing && editContent !== message.content) {
      setEditContent(message.content);
    }
  }, [message.content, isEditing]);

  const isUser = message.role === 'user';
  const isAssistant = message.role === 'assistant';

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  };

  const handleEdit = () => {
    setIsEditing(true);
    setEditContent(message.content);
  };

  const handleSaveEdit = async () => {
    const trimmedContent = editContent.trim();

    if (trimmedContent) {
      // For user messages, resend with the updated content when editing
      if (isUser && onResendMessage) {
        // For user messages, we resend which will also update the content
        console.log('Calling onResendMessage with messageId:', message.id, 'and content:', trimmedContent);
        onResendMessage(message.id, trimmedContent);
      } else {
        // For assistant messages, just update the content
        console.log('Calling onEditMessage with messageId:', message.id, 'and content:', trimmedContent);
        onEditMessage?.(message.id, trimmedContent);
      }
    }
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditContent(message.content);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancelEdit();
    }
  };

  const handleDelete = () => {
    console.log('Delete button clicked for message:', message.id);
    console.log('onDeleteMessage function:', onDeleteMessage);
    console.log('onDeleteMessage type:', typeof onDeleteMessage);
    if (window.confirm('Are you sure you want to delete this message?')) {
      console.log('User confirmed deletion, calling onDeleteMessage');
      if (onDeleteMessage) {
        console.log('Calling onDeleteMessage with messageId:', message.id);
        onDeleteMessage(message.id);
        console.log('onDeleteMessage call completed');
      } else {
        console.log('onDeleteMessage is not available!');
      }
    } else {
      console.log('User cancelled deletion');
    }
  };

  return (
    <div
      className={cn(
        "group flex gap-4 p-4 relative",
        isUser ? "bg-transparent" : "bg-muted/30"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Avatar */}
      <div className={cn(
        "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
        isUser ? "bg-blue-600 text-white" : "bg-green-600 text-white"
      )}>
        {isUser ? (
          <UserIcon className="w-5 h-5" />
        ) : (
          <CpuChipIcon className="w-5 h-5" />
        )}
      </div>

      {/* Message Content */}
      <div className="flex-1 min-w-0">
        {/* Role Label */}
        <div className="text-sm font-medium text-muted-foreground mb-1">
          {isUser ? 'You' : 'Nexus'}
        </div>

        {/* Message Text */}
        <div className={cn(
          "text-foreground",
          message.isStreaming && "animate-pulse"
        )}>
          {isEditing ? (
            <div className="space-y-2">
              <textarea
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                onKeyDown={handleKeyDown}
                className="w-full min-h-[100px] p-3 border border-border rounded-lg bg-background text-foreground resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Edit your message..."
                autoFocus
              />
              <div className="flex gap-2">
                <button
                  onClick={handleSaveEdit}
                  className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                >
                  Save {isUser ? '& Resend' : ''}
                </button>
                <button
                  onClick={handleCancelEdit}
                  className="px-3 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            isUser ? (
              // User messages: render as plain text
              <div className="whitespace-pre-wrap break-words text-foreground">
                {(() => {
                  console.log(`MessageBubble ${message.id} rendering content:`, {
                    messageId: message.id,
                    content: message.content,
                    timestamp: message.timestamp,
                    isEditing
                  });
                  return message.content;
                })()}
              </div>
            ) : (
              // Assistant messages: render as markdown or show loading for empty streaming messages
              <div className="max-w-none">
                {message.content === '' && message.isStreaming ? (
                  // Show loading indicator for empty streaming messages (simple mode)
                  <div className="flex items-center space-x-2 text-muted-foreground">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-muted-foreground rounded-full animate-pulse"></div>
                      <div className="w-2 h-2 bg-muted-foreground rounded-full animate-pulse delay-100"></div>
                      <div className="w-2 h-2 bg-muted-foreground rounded-full animate-pulse delay-200"></div>
                    </div>
                    <span className="text-sm">Thinking...</span>
                  </div>
                ) : (
                  (() => {
                    try {
                      return (
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm, remarkMath]}
                      rehypePlugins={[
                        [rehypeKatex, {
                          throwOnError: false,
                          errorColor: '#cc0000',
                          strict: false,
                          trust: true,
                          macros: {}
                        }]
                      ]}
                      components={{
                        // Headers
                        h1: ({ children }) => (
                          <h1 className="text-2xl font-bold mb-4 mt-6 text-foreground">
                            {children}
                          </h1>
                        ),
                        h2: ({ children }) => (
                          <h2 className="text-xl font-bold mb-3 mt-5 text-foreground">
                            {children}
                          </h2>
                        ),
                        h3: ({ children }) => (
                          <h3 className="text-lg font-semibold mb-2 mt-4 text-foreground">
                            {children}
                          </h3>
                        ),
                        // Strong/Bold text
                        strong: ({ children }) => (
                          <strong className="font-bold text-foreground">
                            {children}
                          </strong>
                        ),
                        // Tables
                        table: ({ children }) => (
                          <div className="my-4 overflow-x-auto">
                            <table className="min-w-full border-collapse border border-border rounded-lg">
                              {children}
                            </table>
                          </div>
                        ),
                        thead: ({ children }) => (
                          <thead className="bg-muted/50">
                            {children}
                          </thead>
                        ),
                        th: ({ children }) => (
                          <th className="border border-border px-4 py-2 text-left font-semibold text-foreground">
                            {children}
                          </th>
                        ),
                        td: ({ children }) => (
                          <td className="border border-border px-4 py-2 text-foreground">
                            {children}
                          </td>
                        ),
                        // Lists
                        ul: ({ children }) => (
                          <ul className="list-disc list-outside ml-6 mb-4 text-foreground">
                            {children}
                          </ul>
                        ),
                        ol: ({ children }) => (
                          <ol className="list-decimal list-outside ml-6 mb-4 text-foreground">
                            {children}
                          </ol>
                        ),
                        // Paragraphs
                        p: ({ children }) => (
                          <p className="mb-4 text-foreground leading-relaxed">
                            {children}
                          </p>
                        ),
                        // Code blocks - handle mermaid diagrams
                        code: ({ node, inline, className, children, ...props }) => {
                          const match = /language-(\w+)/.exec(className || '');
                          const language = match ? match[1] : '';
                          const codeContent = String(children).replace(/\n$/, '');

                          // Handle mermaid diagrams
                          if (language === 'mermaid') {
                            return <MermaidChart chart={codeContent} className="my-4" />;
                          }

                          // Handle inline code
                          if (inline) {
                            return (
                              <code
                                className="bg-muted px-1.5 py-0.5 rounded text-sm font-mono text-foreground"
                                {...props}
                              >
                                {children}
                              </code>
                            );
                          }

                          // Handle code blocks
                          return (
                            <div className="my-4">
                              <pre className="bg-muted p-4 rounded-lg overflow-x-auto">
                                <code
                                  className={`font-mono text-sm text-foreground ${className || ''}`}
                                  {...props}
                                >
                                  {children}
                                </code>
                              </pre>
                            </div>
                          );
                        },
                      }}
                    >
                      {message.content}
                    </ReactMarkdown>
                  );
                } catch (error) {
                  console.error('ReactMarkdown error:', error);
                  return (
                    <div className="whitespace-pre-wrap break-words text-foreground">
                      {message.content}
                    </div>
                  );
                    }
                  })()
                )}
              </div>
            )
          )}
          {message.isStreaming && (
            <span className="inline-block w-2 h-4 bg-muted-foreground ml-1 animate-pulse" />
          )}
        </div>
      </div>

      {/* Action Buttons */}
      {!message.isStreaming && (
        <div className={cn(
          "absolute top-2 right-2 flex gap-1 bg-background/90 backdrop-blur-sm rounded-lg p-1 shadow-sm border border-border transition-opacity duration-200",
          isHovered ? "opacity-100" : "opacity-0"
        )}>
          <button
            onClick={handleCopy}
            className="p-1.5 hover:bg-accent rounded-md transition-colors"
            title="Copy message"
          >
            {copied ? (
              <CheckIcon className="w-4 h-4 text-green-600" />
            ) : (
              <ClipboardIcon className="w-4 h-4 text-muted-foreground hover:text-foreground" />
            )}
          </button>
          {!isEditing && (onEditMessage || (isUser && onResendMessage)) && (
            <button
              onClick={handleEdit}
              className="p-1.5 hover:bg-accent rounded-md transition-colors"
              title={isUser ? "Edit and resend" : "Edit message"}
            >
              <PencilIcon className="w-4 h-4 text-muted-foreground hover:text-foreground" />
            </button>
          )}
          {!isEditing && onDeleteMessage && (
            <button
              onClick={handleDelete}
              className="p-1.5 hover:bg-red-500/20 rounded-md transition-colors"
              title="Delete message"
            >
              <TrashIcon className="w-4 h-4 text-red-500 hover:text-red-400" />
            </button>
          )}
        </div>
      )}
    </div>
  );
}
