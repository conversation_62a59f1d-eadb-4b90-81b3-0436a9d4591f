"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ProgressIndicator.tsx":
/*!**********************************************!*\
  !*** ./src/components/ProgressIndicator.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProgressIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CpuChipIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CpuChipIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ProgressIndicator(param) {\n    let { isVisible, currentStatus, mode = 'enhanced' } = param;\n    _s();\n    const [dots, setDots] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProgressIndicator.useEffect\": ()=>{\n            if (!isVisible) return;\n            const interval = setInterval({\n                \"ProgressIndicator.useEffect.interval\": ()=>{\n                    setDots({\n                        \"ProgressIndicator.useEffect.interval\": (prev)=>{\n                            if (prev.length >= 3) return '';\n                            return prev + '.';\n                        }\n                    }[\"ProgressIndicator.useEffect.interval\"]);\n                }\n            }[\"ProgressIndicator.useEffect.interval\"], 500);\n            return ({\n                \"ProgressIndicator.useEffect\": ()=>clearInterval(interval)\n            })[\"ProgressIndicator.useEffect\"];\n        }\n    }[\"ProgressIndicator.useEffect\"], [\n        isVisible\n    ]);\n    if (!isVisible) return null;\n    const getStatusIcon = (status)=>{\n        if (status.includes('🚀') || status.includes('Starting')) return '🚀';\n        if (status.includes('🧠') || status.includes('Breaking')) return '🧠';\n        if (status.includes('⚡') || status.includes('Launching')) return '⚡';\n        if (status.includes('🔄') || status.includes('Synthesizing')) return '🔄';\n        if (status.includes('🔍') || status.includes('search')) return '🔍';\n        if (status.includes('🧮') || status.includes('calculat')) return '🧮';\n        if (status.includes('📁') || status.includes('file')) return '📁';\n        return '⚙️';\n    };\n    const getModeConfig = ()=>{\n        if (mode === 'orchestrator') {\n            return {\n                title: 'Research Mode Active',\n                subtitle: 'Multi-agent analysis in progress',\n                color: 'purple',\n                bgColor: 'bg-purple-50',\n                borderColor: 'border-purple-200',\n                textColor: 'text-purple-800',\n                iconColor: 'text-purple-600'\n            };\n        }\n        return {\n            title: 'Enhanced Mode Active',\n            subtitle: 'AI tools and capabilities enabled',\n            color: 'blue',\n            bgColor: 'bg-blue-50',\n            borderColor: 'border-blue-200',\n            textColor: 'text-blue-800',\n            iconColor: 'text-blue-600'\n        };\n    };\n    const config = getModeConfig();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"group flex gap-4 p-4 relative bg-muted/30\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center bg-green-600 text-white mt-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CpuChipIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium text-muted-foreground mb-1\",\n                        children: \"Nexus\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\\n              w-4 h-4 border-2 border-\".concat(config.color, \"-200 border-t-\").concat(config.color, \"-600\\n              rounded-full animate-spin\\n            \")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-foreground\",\n                                                        children: config.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: dots\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: config.subtitle\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this),\n                            currentStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: getStatusIcon(currentStatus)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: currentStatus\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this),\n                            mode === 'orchestrator' && !currentStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1 mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-xs text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-purple-400 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Analyzing query complexity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-xs text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-purple-300 rounded-full animate-pulse delay-100\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Deploying specialized agents\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-xs text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-purple-200 rounded-full animate-pulse delay-200\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Gathering comprehensive insights\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this),\n                            mode === 'enhanced' && !currentStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1 mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-xs text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Processing with AI tools\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-xs text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-blue-300 rounded-full animate-pulse delay-100\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Accessing real-time information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this),\n                            mode === 'orchestrator' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full bg-purple-200 rounded-full h-1.5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-600 h-1.5 rounded-full animate-pulse\",\n                                        style: {\n                                            width: '60%'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n_s(ProgressIndicator, \"x5A7Sg53ZjAlz59b/ycs4VK0Y+g=\");\n_c = ProgressIndicator;\nvar _c;\n$RefreshReg$(_c, \"ProgressIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1Byb2dyZXNzSW5kaWNhdG9yLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUU0QztBQUNjO0FBQ3pCO0FBUWxCLFNBQVNJLGtCQUFrQixLQUlqQjtRQUppQixFQUN4Q0MsU0FBUyxFQUNUQyxhQUFhLEVBQ2JDLE9BQU8sVUFBVSxFQUNNLEdBSmlCOztJQUt4QyxNQUFNLENBQUNDLE1BQU1DLFFBQVEsR0FBR1QsK0NBQVFBLENBQUM7SUFFakNDLGdEQUFTQTt1Q0FBQztZQUNSLElBQUksQ0FBQ0ksV0FBVztZQUVoQixNQUFNSyxXQUFXQzt3REFBWTtvQkFDM0JGO2dFQUFRRyxDQUFBQTs0QkFDTixJQUFJQSxLQUFLQyxNQUFNLElBQUksR0FBRyxPQUFPOzRCQUM3QixPQUFPRCxPQUFPO3dCQUNoQjs7Z0JBQ0Y7dURBQUc7WUFFSDsrQ0FBTyxJQUFNRSxjQUFjSjs7UUFDN0I7c0NBQUc7UUFBQ0w7S0FBVTtJQUVkLElBQUksQ0FBQ0EsV0FBVyxPQUFPO0lBRXZCLE1BQU1VLGdCQUFnQixDQUFDQztRQUNyQixJQUFJQSxPQUFPQyxRQUFRLENBQUMsU0FBU0QsT0FBT0MsUUFBUSxDQUFDLGFBQWEsT0FBTztRQUNqRSxJQUFJRCxPQUFPQyxRQUFRLENBQUMsU0FBU0QsT0FBT0MsUUFBUSxDQUFDLGFBQWEsT0FBTztRQUNqRSxJQUFJRCxPQUFPQyxRQUFRLENBQUMsUUFBUUQsT0FBT0MsUUFBUSxDQUFDLGNBQWMsT0FBTztRQUNqRSxJQUFJRCxPQUFPQyxRQUFRLENBQUMsU0FBU0QsT0FBT0MsUUFBUSxDQUFDLGlCQUFpQixPQUFPO1FBQ3JFLElBQUlELE9BQU9DLFFBQVEsQ0FBQyxTQUFTRCxPQUFPQyxRQUFRLENBQUMsV0FBVyxPQUFPO1FBQy9ELElBQUlELE9BQU9DLFFBQVEsQ0FBQyxTQUFTRCxPQUFPQyxRQUFRLENBQUMsYUFBYSxPQUFPO1FBQ2pFLElBQUlELE9BQU9DLFFBQVEsQ0FBQyxTQUFTRCxPQUFPQyxRQUFRLENBQUMsU0FBUyxPQUFPO1FBQzdELE9BQU87SUFDVDtJQUVBLE1BQU1DLGdCQUFnQjtRQUNwQixJQUFJWCxTQUFTLGdCQUFnQjtZQUMzQixPQUFPO2dCQUNMWSxPQUFPO2dCQUNQQyxVQUFVO2dCQUNWQyxPQUFPO2dCQUNQQyxTQUFTO2dCQUNUQyxhQUFhO2dCQUNiQyxXQUFXO2dCQUNYQyxXQUFXO1lBQ2I7UUFDRjtRQUVBLE9BQU87WUFDTE4sT0FBTztZQUNQQyxVQUFVO1lBQ1ZDLE9BQU87WUFDUEMsU0FBUztZQUNUQyxhQUFhO1lBQ2JDLFdBQVc7WUFDWEMsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNQyxTQUFTUjtJQUVmLHFCQUNFLDhEQUFDUztRQUFJQyxXQUFXekIsOENBQUVBLENBQ2hCOzswQkFHQSw4REFBQ3dCO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDMUIscUdBQVdBO29CQUFDMEIsV0FBVTs7Ozs7Ozs7Ozs7MEJBSXpCLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUFpRDs7Ozs7O2tDQUtoRSw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBRWIsOERBQUNEO3dDQUFJQyxXQUFXLDJDQUN5Q0YsT0FBN0JBLE9BQU9MLEtBQUssRUFBQyxrQkFBNkIsT0FBYkssT0FBT0wsS0FBSyxFQUFDOzs7Ozs7a0RBSXRFLDhEQUFDTTt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0M7d0RBQUtELFdBQVk7a0VBQ2ZGLE9BQU9QLEtBQUs7Ozs7OztrRUFFZiw4REFBQ1U7d0RBQUtELFdBQVU7a0VBQ2JwQjs7Ozs7Ozs7Ozs7OzBEQUlMLDhEQUFDc0I7Z0RBQUVGLFdBQVU7MERBQ1ZGLE9BQU9OLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs0QkFLckJkLCtCQUNDLDhEQUFDcUI7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDQzt3Q0FBS0QsV0FBVTtrREFDYmIsY0FBY1Q7Ozs7OztrREFFakIsOERBQUN1Qjt3Q0FBS0QsV0FBVTtrREFDYnRCOzs7Ozs7Ozs7Ozs7NEJBS05DLFNBQVMsa0JBQWtCLENBQUNELCtCQUMzQiw4REFBQ3FCO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7Ozs7OzBEQUNmLDhEQUFDQzswREFBSzs7Ozs7Ozs7Ozs7O2tEQUVSLDhEQUFDRjt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzs7Ozs7MERBQ2YsOERBQUNDOzBEQUFLOzs7Ozs7Ozs7Ozs7a0RBRVIsOERBQUNGO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7Ozs7OzswREFDZiw4REFBQ0M7MERBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs0QkFLWHRCLFNBQVMsY0FBYyxDQUFDRCwrQkFDdkIsOERBQUNxQjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7Ozs7OzswREFDZiw4REFBQ0M7MERBQUs7Ozs7Ozs7Ozs7OztrREFFUiw4REFBQ0Y7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7Ozs7OzBEQUNmLDhEQUFDQzswREFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzRCQU1YdEIsU0FBUyxnQ0FDUiw4REFBQ29CO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7d0NBQ1ZHLE9BQU87NENBQUVDLE9BQU87d0NBQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRM0M7R0ExSndCNUI7S0FBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRGV2XFxBSVxcQ2hhdFxcY2hhdDExXFxhaS1jaGF0LWFwcFxcc3JjXFxjb21wb25lbnRzXFxQcm9ncmVzc0luZGljYXRvci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQ3B1Q2hpcEljb24gfSBmcm9tICdAaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUnO1xuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscyc7XG5cbmludGVyZmFjZSBQcm9ncmVzc0luZGljYXRvclByb3BzIHtcbiAgaXNWaXNpYmxlOiBib29sZWFuO1xuICBjdXJyZW50U3RhdHVzPzogc3RyaW5nO1xuICBtb2RlPzogJ2VuaGFuY2VkJyB8ICdvcmNoZXN0cmF0b3InO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBQcm9ncmVzc0luZGljYXRvcih7IFxuICBpc1Zpc2libGUsIFxuICBjdXJyZW50U3RhdHVzLCBcbiAgbW9kZSA9ICdlbmhhbmNlZCcgXG59OiBQcm9ncmVzc0luZGljYXRvclByb3BzKSB7XG4gIGNvbnN0IFtkb3RzLCBzZXREb3RzXSA9IHVzZVN0YXRlKCcnKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghaXNWaXNpYmxlKSByZXR1cm47XG5cbiAgICBjb25zdCBpbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHtcbiAgICAgIHNldERvdHMocHJldiA9PiB7XG4gICAgICAgIGlmIChwcmV2Lmxlbmd0aCA+PSAzKSByZXR1cm4gJyc7XG4gICAgICAgIHJldHVybiBwcmV2ICsgJy4nO1xuICAgICAgfSk7XG4gICAgfSwgNTAwKTtcblxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKGludGVydmFsKTtcbiAgfSwgW2lzVmlzaWJsZV0pO1xuXG4gIGlmICghaXNWaXNpYmxlKSByZXR1cm4gbnVsbDtcblxuICBjb25zdCBnZXRTdGF0dXNJY29uID0gKHN0YXR1czogc3RyaW5nKSA9PiB7XG4gICAgaWYgKHN0YXR1cy5pbmNsdWRlcygn8J+agCcpIHx8IHN0YXR1cy5pbmNsdWRlcygnU3RhcnRpbmcnKSkgcmV0dXJuICfwn5qAJztcbiAgICBpZiAoc3RhdHVzLmluY2x1ZGVzKCfwn6egJykgfHwgc3RhdHVzLmluY2x1ZGVzKCdCcmVha2luZycpKSByZXR1cm4gJ/Cfp6AnO1xuICAgIGlmIChzdGF0dXMuaW5jbHVkZXMoJ+KaoScpIHx8IHN0YXR1cy5pbmNsdWRlcygnTGF1bmNoaW5nJykpIHJldHVybiAn4pqhJztcbiAgICBpZiAoc3RhdHVzLmluY2x1ZGVzKCfwn5SEJykgfHwgc3RhdHVzLmluY2x1ZGVzKCdTeW50aGVzaXppbmcnKSkgcmV0dXJuICfwn5SEJztcbiAgICBpZiAoc3RhdHVzLmluY2x1ZGVzKCfwn5SNJykgfHwgc3RhdHVzLmluY2x1ZGVzKCdzZWFyY2gnKSkgcmV0dXJuICfwn5SNJztcbiAgICBpZiAoc3RhdHVzLmluY2x1ZGVzKCfwn6euJykgfHwgc3RhdHVzLmluY2x1ZGVzKCdjYWxjdWxhdCcpKSByZXR1cm4gJ/Cfp64nO1xuICAgIGlmIChzdGF0dXMuaW5jbHVkZXMoJ/Cfk4EnKSB8fCBzdGF0dXMuaW5jbHVkZXMoJ2ZpbGUnKSkgcmV0dXJuICfwn5OBJztcbiAgICByZXR1cm4gJ+Kame+4jyc7XG4gIH07XG5cbiAgY29uc3QgZ2V0TW9kZUNvbmZpZyA9ICgpID0+IHtcbiAgICBpZiAobW9kZSA9PT0gJ29yY2hlc3RyYXRvcicpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHRpdGxlOiAnUmVzZWFyY2ggTW9kZSBBY3RpdmUnLFxuICAgICAgICBzdWJ0aXRsZTogJ011bHRpLWFnZW50IGFuYWx5c2lzIGluIHByb2dyZXNzJyxcbiAgICAgICAgY29sb3I6ICdwdXJwbGUnLFxuICAgICAgICBiZ0NvbG9yOiAnYmctcHVycGxlLTUwJyxcbiAgICAgICAgYm9yZGVyQ29sb3I6ICdib3JkZXItcHVycGxlLTIwMCcsXG4gICAgICAgIHRleHRDb2xvcjogJ3RleHQtcHVycGxlLTgwMCcsXG4gICAgICAgIGljb25Db2xvcjogJ3RleHQtcHVycGxlLTYwMCdcbiAgICAgIH07XG4gICAgfVxuICAgIFxuICAgIHJldHVybiB7XG4gICAgICB0aXRsZTogJ0VuaGFuY2VkIE1vZGUgQWN0aXZlJyxcbiAgICAgIHN1YnRpdGxlOiAnQUkgdG9vbHMgYW5kIGNhcGFiaWxpdGllcyBlbmFibGVkJyxcbiAgICAgIGNvbG9yOiAnYmx1ZScsXG4gICAgICBiZ0NvbG9yOiAnYmctYmx1ZS01MCcsXG4gICAgICBib3JkZXJDb2xvcjogJ2JvcmRlci1ibHVlLTIwMCcsXG4gICAgICB0ZXh0Q29sb3I6ICd0ZXh0LWJsdWUtODAwJyxcbiAgICAgIGljb25Db2xvcjogJ3RleHQtYmx1ZS02MDAnXG4gICAgfTtcbiAgfTtcblxuICBjb25zdCBjb25maWcgPSBnZXRNb2RlQ29uZmlnKCk7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17Y24oXG4gICAgICBcImdyb3VwIGZsZXggZ2FwLTQgcC00IHJlbGF0aXZlIGJnLW11dGVkLzMwXCJcbiAgICApfT5cbiAgICAgIHsvKiBBdmF0YXIgLSBtYXRjaGluZyBNZXNzYWdlQnViYmxlIHN0eWxlIHdpdGggcHJvcGVyIGFsaWdubWVudCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMCB3LTggaC04IHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBiZy1ncmVlbi02MDAgdGV4dC13aGl0ZSBtdC0wXCI+XG4gICAgICAgIDxDcHVDaGlwSWNvbiBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogTWVzc2FnZSBDb250ZW50IC0gbWF0Y2hpbmcgTWVzc2FnZUJ1YmJsZSBzdHJ1Y3R1cmUgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBtaW4tdy0wXCI+XG4gICAgICAgIHsvKiBSb2xlIExhYmVsIC0gbWF0Y2hpbmcgTWVzc2FnZUJ1YmJsZSAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBtYi0xXCI+XG4gICAgICAgICAgTmV4dXNcbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFByb2dyZXNzIENvbnRlbnQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgbWItMlwiPlxuICAgICAgICAgICAgey8qIEFuaW1hdGVkIHNwaW5uZXIgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YFxuICAgICAgICAgICAgICB3LTQgaC00IGJvcmRlci0yIGJvcmRlci0ke2NvbmZpZy5jb2xvcn0tMjAwIGJvcmRlci10LSR7Y29uZmlnLmNvbG9yfS02MDBcbiAgICAgICAgICAgICAgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpblxuICAgICAgICAgICAgYH0+PC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWZvcmVncm91bmRgfT5cbiAgICAgICAgICAgICAgICAgIHtjb25maWcudGl0bGV9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICB7ZG90c31cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAge2NvbmZpZy5zdWJ0aXRsZX1cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Y3VycmVudFN0YXR1cyAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAge2dldFN0YXR1c0ljb24oY3VycmVudFN0YXR1cyl9XG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICAgICAge2N1cnJlbnRTdGF0dXN9XG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7bW9kZSA9PT0gJ29yY2hlc3RyYXRvcicgJiYgIWN1cnJlbnRTdGF0dXMgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTEgbXQtMlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiB0ZXh0LXhzIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctcHVycGxlLTQwMCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1wdWxzZVwiPjwvZGl2PlxuICAgICAgICAgICAgICAgIDxzcGFuPkFuYWx5emluZyBxdWVyeSBjb21wbGV4aXR5PC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgdGV4dC14cyB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLXB1cnBsZS0zMDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtcHVsc2UgZGVsYXktMTAwXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgPHNwYW4+RGVwbG95aW5nIHNwZWNpYWxpemVkIGFnZW50czwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHRleHQteHMgdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1wdXJwbGUtMjAwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlIGRlbGF5LTIwMFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgIDxzcGFuPkdhdGhlcmluZyBjb21wcmVoZW5zaXZlIGluc2lnaHRzPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7bW9kZSA9PT0gJ2VuaGFuY2VkJyAmJiAhY3VycmVudFN0YXR1cyAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMSBtdC0yXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHRleHQteHMgdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1ibHVlLTQwMCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1wdWxzZVwiPjwvZGl2PlxuICAgICAgICAgICAgICAgIDxzcGFuPlByb2Nlc3Npbmcgd2l0aCBBSSB0b29sczwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHRleHQteHMgdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1ibHVlLTMwMCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1wdWxzZSBkZWxheS0xMDBcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICA8c3Bhbj5BY2Nlc3NpbmcgcmVhbC10aW1lIGluZm9ybWF0aW9uPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7LyogUHJvZ3Jlc3MgYmFyIGZvciBvcmNoZXN0cmF0b3IgbW9kZSAqL31cbiAgICAgICAgICB7bW9kZSA9PT0gJ29yY2hlc3RyYXRvcicgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0zXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGJnLXB1cnBsZS0yMDAgcm91bmRlZC1mdWxsIGgtMS41XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1wdXJwbGUtNjAwIGgtMS41IHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlXCJcbiAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiAnNjAlJyB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiQ3B1Q2hpcEljb24iLCJjbiIsIlByb2dyZXNzSW5kaWNhdG9yIiwiaXNWaXNpYmxlIiwiY3VycmVudFN0YXR1cyIsIm1vZGUiLCJkb3RzIiwic2V0RG90cyIsImludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJwcmV2IiwibGVuZ3RoIiwiY2xlYXJJbnRlcnZhbCIsImdldFN0YXR1c0ljb24iLCJzdGF0dXMiLCJpbmNsdWRlcyIsImdldE1vZGVDb25maWciLCJ0aXRsZSIsInN1YnRpdGxlIiwiY29sb3IiLCJiZ0NvbG9yIiwiYm9yZGVyQ29sb3IiLCJ0ZXh0Q29sb3IiLCJpY29uQ29sb3IiLCJjb25maWciLCJkaXYiLCJjbGFzc05hbWUiLCJzcGFuIiwicCIsInN0eWxlIiwid2lkdGgiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProgressIndicator.tsx\n"));

/***/ })

});