"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/MessageBubble.tsx":
/*!******************************************!*\
  !*** ./src/components/MessageBubble.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MessageBubble)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ClipboardIcon_CpuChipIcon_PencilIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ClipboardIcon,CpuChipIcon,PencilIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ClipboardIcon_CpuChipIcon_PencilIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ClipboardIcon,CpuChipIcon,PencilIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ClipboardIcon_CpuChipIcon_PencilIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ClipboardIcon,CpuChipIcon,PencilIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ClipboardIcon_CpuChipIcon_PencilIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ClipboardIcon,CpuChipIcon,PencilIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ClipboardIcon_CpuChipIcon_PencilIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ClipboardIcon,CpuChipIcon,PencilIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ClipboardIcon_CpuChipIcon_PencilIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ClipboardIcon,CpuChipIcon,PencilIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var remark_math__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! remark-math */ \"(app-pages-browser)/./node_modules/remark-math/lib/index.js\");\n/* harmony import */ var rehype_katex__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rehype-katex */ \"(app-pages-browser)/./node_modules/rehype-katex/lib/index.js\");\n/* harmony import */ var _MermaidChart__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MermaidChart */ \"(app-pages-browser)/./src/components/MermaidChart.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction MessageBubble(param) {\n    let { message, onEditMessage, onResendMessage, onDeleteMessage } = param;\n    _s();\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editContent, setEditContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(message.content);\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug: Log when component receives new message prop (only on message ID change)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageBubble.useEffect\": ()=>{\n            console.log('MessageBubble received new message:', {\n                messageId: message.id,\n                role: message.role,\n                contentLength: message.content.length\n            });\n        }\n    }[\"MessageBubble.useEffect\"], [\n        message.id\n    ]);\n    // Update editContent when message content changes (but not during editing)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageBubble.useEffect\": ()=>{\n            if (!isEditing && editContent !== message.content) {\n                setEditContent(message.content);\n            }\n        }\n    }[\"MessageBubble.useEffect\"], [\n        message.content,\n        isEditing\n    ]);\n    const isUser = message.role === 'user';\n    const isAssistant = message.role === 'assistant';\n    const handleCopy = async ()=>{\n        try {\n            await navigator.clipboard.writeText(message.content);\n            setCopied(true);\n            setTimeout(()=>setCopied(false), 2000);\n        } catch (err) {\n            console.error('Failed to copy text:', err);\n        }\n    };\n    const handleEdit = ()=>{\n        setIsEditing(true);\n        setEditContent(message.content);\n    };\n    const handleSaveEdit = async ()=>{\n        const trimmedContent = editContent.trim();\n        if (trimmedContent) {\n            // For user messages, resend with the updated content when editing\n            if (isUser && onResendMessage) {\n                // For user messages, we resend which will also update the content\n                console.log('Calling onResendMessage with messageId:', message.id, 'and content:', trimmedContent);\n                onResendMessage(message.id, trimmedContent);\n            } else {\n                // For assistant messages, just update the content\n                console.log('Calling onEditMessage with messageId:', message.id, 'and content:', trimmedContent);\n                onEditMessage === null || onEditMessage === void 0 ? void 0 : onEditMessage(message.id, trimmedContent);\n            }\n        }\n        setIsEditing(false);\n    };\n    const handleCancelEdit = ()=>{\n        setIsEditing(false);\n        setEditContent(message.content);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {\n            e.preventDefault();\n            handleSaveEdit();\n        } else if (e.key === 'Escape') {\n            e.preventDefault();\n            handleCancelEdit();\n        }\n    };\n    const handleDelete = ()=>{\n        console.log('Delete button clicked for message:', message.id);\n        console.log('onDeleteMessage function:', onDeleteMessage);\n        console.log('onDeleteMessage type:', typeof onDeleteMessage);\n        if (window.confirm('Are you sure you want to delete this message?')) {\n            console.log('User confirmed deletion, calling onDeleteMessage');\n            if (onDeleteMessage) {\n                console.log('Calling onDeleteMessage with messageId:', message.id);\n                onDeleteMessage(message.id);\n                console.log('onDeleteMessage call completed');\n            } else {\n                console.log('onDeleteMessage is not available!');\n            }\n        } else {\n            console.log('User cancelled deletion');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"group flex gap-4 p-4 relative\", isUser ? \"bg-transparent\" : \"bg-muted/30\"),\n        onMouseEnter: ()=>setIsHovered(true),\n        onMouseLeave: ()=>setIsHovered(false),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center\", isUser ? \"bg-blue-600 text-white\" : \"bg-green-600 text-white\"),\n                children: isUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ClipboardIcon_CpuChipIcon_PencilIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ClipboardIcon_CpuChipIcon_PencilIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium text-muted-foreground mb-1\",\n                        children: isUser ? 'You' : 'Nexus'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-foreground\", message.isStreaming && \"animate-pulse\"),\n                        children: [\n                            isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: editContent,\n                                        onChange: (e)=>setEditContent(e.target.value),\n                                        onKeyDown: handleKeyDown,\n                                        className: \"w-full min-h-[100px] p-3 border border-border rounded-lg bg-background text-foreground resize-none focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        placeholder: \"Edit your message...\",\n                                        autoFocus: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleSaveEdit,\n                                                className: \"px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\",\n                                                children: [\n                                                    \"Save \",\n                                                    isUser ? '& Resend' : ''\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleCancelEdit,\n                                                className: \"px-3 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this) : isUser ? // User messages: render as plain text\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"whitespace-pre-wrap break-words text-foreground\",\n                                children: (()=>{\n                                    console.log(\"MessageBubble \".concat(message.id, \" rendering content:\"), {\n                                        messageId: message.id,\n                                        content: message.content,\n                                        timestamp: message.timestamp,\n                                        isEditing\n                                    });\n                                    return message.content;\n                                })()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 15\n                            }, this) : // Assistant messages: render as markdown or show loading for empty streaming messages\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-none\",\n                                children: message.content === '' && message.isStreaming ? // Show loading indicator for empty streaming messages (simple mode)\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-muted-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-muted-foreground rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-muted-foreground rounded-full animate-pulse delay-100\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-muted-foreground rounded-full animate-pulse delay-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm\",\n                                            children: \"Thinking...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 19\n                                }, this) : (()=>{\n                                    try {\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_6__.Markdown, {\n                                            remarkPlugins: [\n                                                remark_gfm__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                                remark_math__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                                            ],\n                                            rehypePlugins: [\n                                                [\n                                                    rehype_katex__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                                    {\n                                                        throwOnError: false,\n                                                        errorColor: '#cc0000',\n                                                        strict: false,\n                                                        trust: true,\n                                                        macros: {}\n                                                    }\n                                                ]\n                                            ],\n                                            components: {\n                                                // Headers\n                                                h1: (param)=>{\n                                                    let { children } = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold mb-4 mt-6 text-foreground\",\n                                                        children: children\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 27\n                                                    }, void 0);\n                                                },\n                                                h2: (param)=>{\n                                                    let { children } = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-bold mb-3 mt-5 text-foreground\",\n                                                        children: children\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 27\n                                                    }, void 0);\n                                                },\n                                                h3: (param)=>{\n                                                    let { children } = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-2 mt-4 text-foreground\",\n                                                        children: children\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 27\n                                                    }, void 0);\n                                                },\n                                                // Strong/Bold text\n                                                strong: (param)=>{\n                                                    let { children } = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"font-bold text-foreground\",\n                                                        children: children\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 27\n                                                    }, void 0);\n                                                },\n                                                // Tables\n                                                table: (param)=>{\n                                                    let { children } = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"my-4 overflow-x-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                            className: \"min-w-full border-collapse border border-border rounded-lg\",\n                                                            children: children\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 29\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 27\n                                                    }, void 0);\n                                                },\n                                                thead: (param)=>{\n                                                    let { children } = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                        className: \"bg-muted/50\",\n                                                        children: children\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 27\n                                                    }, void 0);\n                                                },\n                                                th: (param)=>{\n                                                    let { children } = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-border px-4 py-2 text-left font-semibold text-foreground\",\n                                                        children: children\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 27\n                                                    }, void 0);\n                                                },\n                                                td: (param)=>{\n                                                    let { children } = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"border border-border px-4 py-2 text-foreground\",\n                                                        children: children\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 27\n                                                    }, void 0);\n                                                },\n                                                // Lists\n                                                ul: (param)=>{\n                                                    let { children } = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"list-disc list-outside ml-6 mb-4 text-foreground\",\n                                                        children: children\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 27\n                                                    }, void 0);\n                                                },\n                                                ol: (param)=>{\n                                                    let { children } = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                        className: \"list-decimal list-outside ml-6 mb-4 text-foreground\",\n                                                        children: children\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 27\n                                                    }, void 0);\n                                                },\n                                                // Paragraphs\n                                                p: (param)=>{\n                                                    let { children } = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mb-4 text-foreground leading-relaxed\",\n                                                        children: children\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 27\n                                                    }, void 0);\n                                                },\n                                                // Code blocks - handle mermaid diagrams\n                                                code: (param)=>{\n                                                    let { node, inline, className, children, ...props } = param;\n                                                    const match = /language-(\\w+)/.exec(className || '');\n                                                    const language = match ? match[1] : '';\n                                                    const codeContent = String(children).replace(/\\n$/, '');\n                                                    // Handle mermaid diagrams\n                                                    if (language === 'mermaid') {\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MermaidChart__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            chart: codeContent,\n                                                            className: \"my-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 36\n                                                        }, void 0);\n                                                    }\n                                                    // Handle inline code\n                                                    if (inline) {\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                            className: \"bg-muted px-1.5 py-0.5 rounded text-sm font-mono text-foreground\",\n                                                            ...props,\n                                                            children: children\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 31\n                                                        }, void 0);\n                                                    }\n                                                    // Handle code blocks\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"my-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            className: \"bg-muted p-4 rounded-lg overflow-x-auto\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                className: \"font-mono text-sm text-foreground \".concat(className || ''),\n                                                                ...props,\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 33\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 31\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 29\n                                                    }, void 0);\n                                                }\n                                            },\n                                            children: message.content\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 21\n                                        }, this);\n                                    } catch (error) {\n                                        console.error('ReactMarkdown error:', error);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"whitespace-pre-wrap break-words text-foreground\",\n                                            children: message.content\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 21\n                                        }, this);\n                                    }\n                                })()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 15\n                            }, this),\n                            message.isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-block w-2 h-4 bg-muted-foreground ml-1 animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            !message.isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"absolute top-2 right-2 flex gap-1 bg-background/90 backdrop-blur-sm rounded-lg p-1 shadow-sm border border-border transition-opacity duration-200\", isHovered ? \"opacity-100\" : \"opacity-0\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCopy,\n                        className: \"p-1.5 hover:bg-accent rounded-md transition-colors\",\n                        title: \"Copy message\",\n                        children: copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ClipboardIcon_CpuChipIcon_PencilIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-4 h-4 text-green-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ClipboardIcon_CpuChipIcon_PencilIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"w-4 h-4 text-muted-foreground hover:text-foreground\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 11\n                    }, this),\n                    !isEditing && (onEditMessage || isUser && onResendMessage) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleEdit,\n                        className: \"p-1.5 hover:bg-accent rounded-md transition-colors\",\n                        title: isUser ? \"Edit and resend\" : \"Edit message\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ClipboardIcon_CpuChipIcon_PencilIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"w-4 h-4 text-muted-foreground hover:text-foreground\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 13\n                    }, this),\n                    !isEditing && onDeleteMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleDelete,\n                        className: \"p-1.5 hover:bg-red-500/20 rounded-md transition-colors\",\n                        title: \"Delete message\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ClipboardIcon_CpuChipIcon_PencilIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"w-4 h-4 text-red-500 hover:text-red-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n                lineNumber: 337,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\MessageBubble.tsx\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, this);\n}\n_s(MessageBubble, \"XKyyQ2oIQ7WNIs/SbwFFhTKq2m8=\");\n_c = MessageBubble;\nvar _c;\n$RefreshReg$(_c, \"MessageBubble\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MessageBubble.tsx\n"));

/***/ })

});